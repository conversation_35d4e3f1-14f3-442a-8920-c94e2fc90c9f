{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\workspaces\\nsl\\back\\srsrmain\\frontend\\build\\.cxx\\Debug\\t53m2r11\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\workspaces\\nsl\\back\\srsrmain\\frontend\\build\\.cxx\\Debug\\t53m2r11\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}