1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srsr.property.srsr_property_management"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:13-50
32-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent> <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
37-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
37-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-74
38    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
38-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
38-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-78
39    <uses-permission android:name="android.permission.WAKE_LOCK" />
39-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
39-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
40    <uses-permission android:name="android.permission.VIBRATE" />
40-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
40-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
41    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
41-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
41-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
42    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
42-->[:connectivity_plus] D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
42-->[:connectivity_plus] D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
43
44    <uses-feature
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
45        android:glEsVersion="0x00020000"
45-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
46        android:required="true" />
46-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.srsr.property.srsr_property_management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.srsr.property.srsr_property_management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
55        android:name="android.app.Application"
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
57        android:debuggable="true"
58        android:extractNativeLibs="false"
59        android:icon="@mipmap/ic_launcher"
60        android:label="srsr_property_management" >
61        <activity
62            android:name="com.srsr.property.srsr_property_management.MainActivity"
63            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
64            android:exported="true"
65            android:hardwareAccelerated="true"
66            android:launchMode="singleTop"
67            android:taskAffinity=""
68            android:theme="@style/LaunchTheme"
69            android:windowSoftInputMode="adjustResize" >
70
71            <!--
72                 Specifies an Android theme to apply to this Activity as soon as
73                 the Android process has started. This theme is visible to the user
74                 while the Flutter UI initializes. After that, this theme continues
75                 to determine the Window background behind the Flutter UI.
76            -->
77            <meta-data
78                android:name="io.flutter.embedding.android.NormalTheme"
79                android:resource="@style/NormalTheme" />
80
81            <intent-filter>
82                <action android:name="android.intent.action.MAIN" />
83
84                <category android:name="android.intent.category.LAUNCHER" />
85            </intent-filter>
86        </activity>
87        <!--
88             Don't delete the meta-data below.
89             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
90        -->
91        <meta-data
92            android:name="flutterEmbedding"
93            android:value="2" />
94
95        <service
95-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
96            android:name="com.baseflow.geolocator.GeolocatorLocationService"
96-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
97            android:enabled="true"
97-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
98            android:exported="false"
98-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
99            android:foregroundServiceType="location" />
99-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
100        <service
100-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:44
101            android:name="id.flutter.flutter_background_service.BackgroundService"
101-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-83
102            android:enabled="true"
102-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-35
103            android:exported="true"
103-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-36
104            android:stopWithTask="false" />
104-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-41
105
106        <receiver
106-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-21:39
107            android:name="id.flutter.flutter_background_service.WatchdogReceiver"
107-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-82
108            android:enabled="true"
108-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-35
109            android:exported="true" />
109-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
110        <receiver
110-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-31:20
111            android:name="id.flutter.flutter_background_service.BootReceiver"
111-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-78
112            android:enabled="true"
112-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-35
113            android:exported="true" >
113-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
114            <intent-filter>
114-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-30:29
115                <action android:name="android.intent.action.BOOT_COMPLETED" />
115-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-79
115-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-76
116                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
116-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
116-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
117                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
117-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
117-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
118            </intent-filter>
119        </receiver>
120
121        <provider
121-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
122            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
122-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
123            android:authorities="com.srsr.property.srsr_property_management.flutter.image_provider"
123-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
124            android:exported="false"
124-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
125            android:grantUriPermissions="true" >
125-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
126            <meta-data
126-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
127                android:name="android.support.FILE_PROVIDER_PATHS"
127-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
128                android:resource="@xml/flutter_image_picker_file_paths" />
128-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
129        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
130        <service
130-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
131            android:name="com.google.android.gms.metadata.ModuleDependencies"
131-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
132            android:enabled="false"
132-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
133            android:exported="false" >
133-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
134            <intent-filter>
134-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
135                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
135-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
135-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
136            </intent-filter>
137
138            <meta-data
138-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
139                android:name="photopicker_activity:0:required"
139-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
140                android:value="" />
140-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
141        </service>
142
143        <activity
143-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
144            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
144-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
145            android:exported="false"
145-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
146            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
146-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
147        <uses-library
147-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
148            android:name="org.apache.http.legacy"
148-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
149            android:required="false" />
149-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
150
151        <activity
151-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
152            android:name="com.google.android.gms.common.api.GoogleApiActivity"
152-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
153            android:exported="false"
153-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
154            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
154-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
155
156        <meta-data
156-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
157            android:name="com.google.android.gms.version"
157-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
158            android:value="@integer/google_play_services_version" />
158-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
159
160        <uses-library
160-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
161            android:name="androidx.window.extensions"
161-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
162            android:required="false" />
162-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
163        <uses-library
163-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
164            android:name="androidx.window.sidecar"
164-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
165            android:required="false" />
165-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
166
167        <provider
167-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
168            android:name="androidx.startup.InitializationProvider"
168-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
169            android:authorities="com.srsr.property.srsr_property_management.androidx-startup"
169-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
170            android:exported="false" >
170-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
171            <meta-data
171-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
172-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
173                android:value="androidx.startup" />
173-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
176                android:value="androidx.startup" />
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
177        </provider>
178
179        <receiver
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
180            android:name="androidx.profileinstaller.ProfileInstallReceiver"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
181            android:directBootAware="false"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
182            android:enabled="true"
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
183            android:exported="true"
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
184            android:permission="android.permission.DUMP" >
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
186                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
189                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
192                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
193            </intent-filter>
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
195                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
196            </intent-filter>
197        </receiver>
198    </application>
199
200</manifest>
